<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Wallet extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'balance',
        'pending_balance',
        'total_earned',
        'total_withdrawn',
        'currency',
        'payment_methods',
        'stripe_account_id',
        'paypal_email',
        'is_verified'
    ];

    protected $casts = [
        'payment_methods' => 'array',
        'balance' => 'decimal:2',
        'pending_balance' => 'decimal:2',
        'total_earned' => 'decimal:2',
        'total_withdrawn' => 'decimal:2',
        'is_verified' => 'boolean'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'user_id', 'user_id');
    }

    public function deposit($amount)
    {
        $this->balance += $amount;
        $this->total_earned += $amount;
        $this->save();
    }

    public function withdraw($amount)
    {
        if ($this->balance >= $amount) {
            $this->balance -= $amount;
            $this->total_withdrawn += $amount;
            $this->save();
            return true;
        }
        return false;
    }

    public function addPending($amount)
    {
        $this->pending_balance += $amount;
        $this->save();
    }

    public function clearPending()
    {
        $this->balance += $this->pending_balance;
        $this->pending_balance = 0;
        $this->save();
    }
}
