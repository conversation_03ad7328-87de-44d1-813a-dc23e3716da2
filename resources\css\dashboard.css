/* Dashboard Styles */
.stat-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 transition-all duration-200;
}

.stat-card:hover {
    @apply transform scale-105 shadow-lg;
}

.stat-icon {
    @apply p-3 rounded-full;
}

.video-card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden transition-all duration-200;
}

.video-card:hover {
    @apply shadow-lg transform scale-[1.02];
}

.video-thumbnail {
    @apply relative aspect-video overflow-hidden;
}

.video-thumbnail img {
    @apply w-full h-full object-cover transition-transform duration-200;
}

.video-card:hover .video-thumbnail img {
    @apply scale-105;
}

.video-overlay {
    @apply absolute inset-0 bg-gradient-to-t from-black/80 via-transparent opacity-0 transition-opacity duration-200;
}

.video-card:hover .video-overlay {
    @apply opacity-100;
}

.video-info {
    @apply absolute bottom-0 left-0 right-0 p-4 text-white;
}

.video-actions {
    @apply flex items-center space-x-2;
}

.action-button {
    @apply p-2 text-gray-600 dark:text-gray-300 transition-colors duration-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700;
}

.action-button.edit:hover {
    @apply text-indigo-600 dark:text-indigo-400;
}

.action-button.delete:hover {
    @apply text-red-600 dark:text-red-400;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fadeIn 0.3s ease-out;
} 