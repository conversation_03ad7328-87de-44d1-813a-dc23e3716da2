class VideoUpload {
    constructor() {
        this.form = document.getElementById('videoForm');
        this.videoInput = document.getElementById('videoInput');
        this.uploadArea = document.getElementById('uploadArea');
        this.previewArea = document.getElementById('previewArea');
        this.videoPreview = document.getElementById('videoPreview');
        this.csrfToken = document.querySelector('meta[name="csrf-token"]').content;
        this.maxFileSize = 100 * 1024 * 1024; // 100MB
        this.thumbnailCanvas = document.createElement('canvas');
        this.thumbnailContext = this.thumbnailCanvas.getContext('2d');
        this.thumbnailCanvas.width = 640;
        this.thumbnailCanvas.height = 360;
        
        this.init();
    }
    
    init() {
        this.initDragAndDrop();
        this.initFileSelection();
        this.initFormSubmission();
    }
    
    initDragAndDrop() {
        this.uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            this.uploadArea.classList.add('border-primary');
        });
        
        this.uploadArea.addEventListener('dragleave', () => {
            this.uploadArea.classList.remove('border-primary');
        });
        
        this.uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            this.uploadArea.classList.remove('border-primary');
            const file = e.dataTransfer.files[0];
            if (file && file.type.startsWith('video/')) {
                this.videoInput.files = e.dataTransfer.files;
                this.handleVideoFile(file);
            }
        });
    }
    
    initFileSelection() {
        this.videoInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleVideoFile(file);
            }
        });
    }
    
    initFormSubmission() {
        this.form.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const file = this.videoInput.files[0];
            if (!file) {
                alert('Please select a video file');
                return;
            }
            
            if (file.size > this.maxFileSize) {
                alert('File size exceeds 100MB limit');
                return;
            }
            
            await this.uploadVideo(file);
        });
    }
    
    handleVideoFile(file) {
        if (file.size > this.maxFileSize) {
            alert('File size exceeds 100MB limit');
            return;
        }
        
        const videoUrl = URL.createObjectURL(file);
        this.videoPreview.src = videoUrl;
        this.uploadArea.classList.add('d-none');
        this.previewArea.classList.remove('d-none');
        
        // Add file size display
        const fileSize = this.formatFileSize(file.size);
        const fileSizeElement = document.createElement('div');
        fileSizeElement.className = 'text-muted mt-2';
        fileSizeElement.textContent = `File size: ${fileSize}`;
        this.previewArea.appendChild(fileSizeElement);
        
        // Generate thumbnail
        this.generateThumbnail();
        
        // Add compression options
        this.addCompressionOptions();
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    removeVideo() {
        this.videoInput.value = '';
        this.videoPreview.src = '';
        this.previewArea.classList.add('d-none');
        this.uploadArea.classList.remove('d-none');
        
        const fileSizeElement = this.previewArea.querySelector('.text-muted');
        if (fileSizeElement) {
            fileSizeElement.remove();
        }
    }
    
    generateThumbnail() {
        this.videoPreview.addEventListener('loadeddata', () => {
            // Set canvas dimensions to match video aspect ratio
            const aspectRatio = this.videoPreview.videoWidth / this.videoPreview.videoHeight;
            this.thumbnailCanvas.width = 640;
            this.thumbnailCanvas.height = 640 / aspectRatio;
            
            // Draw video frame on canvas
            this.thumbnailContext.drawImage(this.videoPreview, 0, 0, this.thumbnailCanvas.width, this.thumbnailCanvas.height);
            
            // Add thumbnail preview
            const thumbnailPreview = document.createElement('div');
            thumbnailPreview.className = 'mt-3';
            thumbnailPreview.innerHTML = `
                <label class="form-label">Video Thumbnail</label>
                <div class="position-relative">
                    <img src="${this.thumbnailCanvas.toDataURL('image/jpeg')}" 
                         class="img-fluid rounded" 
                         alt="Video thumbnail">
                    <button type="button" 
                            class="btn btn-light btn-sm position-absolute top-0 end-0 m-2" 
                            onclick="videoUpload.generateNewThumbnail()">
                        <i class="fas fa-sync"></i>
                    </button>
                </div>
                <input type="hidden" name="thumbnail" value="${this.thumbnailCanvas.toDataURL('image/jpeg')}">
            `;
            
            const existingThumbnail = this.previewArea.querySelector('.mt-3');
            if (existingThumbnail) {
                existingThumbnail.remove();
            }
            
            this.previewArea.appendChild(thumbnailPreview);
        });
    }
    
    generateNewThumbnail() {
        // Generate thumbnail at current video time
        this.thumbnailContext.drawImage(this.videoPreview, 0, 0, this.thumbnailCanvas.width, this.thumbnailCanvas.height);
        
        // Update thumbnail preview
        const thumbnailImg = this.previewArea.querySelector('.img-fluid');
        thumbnailImg.src = this.thumbnailCanvas.toDataURL('image/jpeg');
        
        // Update hidden input
        const thumbnailInput = this.previewArea.querySelector('input[name="thumbnail"]');
        thumbnailInput.value = this.thumbnailCanvas.toDataURL('image/jpeg');
    }
    
    addCompressionOptions() {
        const compressionOptions = document.createElement('div');
        compressionOptions.className = 'mt-3';
        compressionOptions.innerHTML = `
            <label class="form-label">Compression Options</label>
            <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="compressVideo" checked>
                <label class="form-check-label" for="compressVideo">
                    Compress video before upload
                </label>
            </div>
            <div class="form-check mb-2">
                <input class="form-check-input" type="checkbox" id="maintainQuality" checked>
                <label class="form-check-label" for="maintainQuality">
                    Maintain video quality
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="removeAudio" checked>
                <label class="form-check-label" for="removeAudio">
                    Remove audio track
                </label>
            </div>
        `;
        
        this.previewArea.appendChild(compressionOptions);
    }
    
    async uploadVideo(file) {
        const formData = new FormData(this.form);
        const progressBar = this.createProgressBar();
        
        // Add compression options to form data
        const compressVideo = document.getElementById('compressVideo').checked;
        const maintainQuality = document.getElementById('maintainQuality').checked;
        const removeAudio = document.getElementById('removeAudio').checked;
        
        formData.append('compress', compressVideo);
        formData.append('maintain_quality', maintainQuality);
        formData.append('remove_audio', removeAudio);
        
        try {
            const xhr = new XMLHttpRequest();
            
            // Handle upload progress
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 100);
                    this.updateProgress(progressBar, percentComplete);
                }
            });
            
            // Handle upload completion
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    const response = JSON.parse(xhr.responseText);
                    window.location.href = `/videos/${response.video.id}`;
                } else {
                    throw new Error('Upload failed');
                }
            });
            
            // Handle upload error
            xhr.addEventListener('error', () => {
                throw new Error('Upload failed');
            });
            
            // Start upload
            xhr.open('POST', this.form.action, true);
            xhr.setRequestHeader('X-CSRF-TOKEN', this.csrfToken);
            xhr.send(formData);
            
        } catch (error) {
            console.error('Error uploading video:', error);
            alert('Failed to upload video. Please try again.');
            progressBar.remove();
        }
    }
    
    createProgressBar() {
        const progressBar = document.createElement('div');
        progressBar.className = 'progress mt-3';
        progressBar.innerHTML = `
            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                 role="progressbar" 
                 style="width: 0%" 
                 aria-valuenow="0" 
                 aria-valuemin="0" 
                 aria-valuemax="100">
                0%
            </div>
        `;
        
        const existingProgress = this.form.querySelector('.progress');
        if (existingProgress) {
            existingProgress.remove();
        }
        
        this.form.appendChild(progressBar);
        return progressBar;
    }
    
    updateProgress(progressBar, percent) {
        const progressBarInner = progressBar.querySelector('.progress-bar');
        progressBarInner.style.width = `${percent}%`;
        progressBarInner.setAttribute('aria-valuenow', percent);
        progressBarInner.textContent = `${percent}%`;
    }
}

// Initialize
const videoUpload = new VideoUpload(); 