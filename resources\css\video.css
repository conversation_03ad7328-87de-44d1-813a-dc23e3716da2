/* Main Container */
.video-feed-container {
    background: linear-gradient(145deg, #f8fafc, #f1f5f9);
    min-height: 100vh;
    padding: 2rem;
}

.dark .video-feed-container {
    background: linear-gradient(145deg, #0f172a, #1e293b);
}

/* Main Content Layout */
.main-content {
    max-width: 1920px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: minmax(0, 1fr) 380px;
    gap: 2.5rem;
    padding: 0 1.5rem;
}

@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        padding: 0 1rem;
    }
}

/* Video Feed Grid */
.video-feed {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    align-content: start;
}

/* Video Card */
.video-card {
    background: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.dark .video-card {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.video-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark .video-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
}

/* Video Thumbnail */
.video-wrapper {
    position: relative;
    width: 100%;
    padding-top: 56.25%;
    background: #000000;
    overflow: hidden;
}

.video-wrapper video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.video-wrapper:hover video {
    transform: scale(1.08);
}

/* Video Duration Badge */
.video-duration {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.75);
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    backdrop-filter: blur(8px);
    z-index: 2;
}

/* Video Info Section */
.video-info {
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Creator Section */
.creator-section {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 0.5rem 0;
}

.creator-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #ffffff;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .creator-avatar {
    border-color: #1e293b;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.video-details {
    flex: 1;
    min-width: 0;
}

/* Video Title */
.video-title {
    font-size: 1.125rem;
    font-weight: 600;
    background: linear-gradient(135deg, #0f172a, #334155);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.dark .video-title {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.creator-name {
    font-size: 0.938rem;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 0.25rem;
    transition: color 0.2s ease;
}

.creator-name:hover {
    color: #3b82f6;
}

.dark .creator-name:hover {
    color: #60a5fa;
}

/* Video Stats */
.video-stats {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    color: #64748b;
    font-size: 0.875rem;
    font-weight: 500;
}

.video-stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.video-stat:hover {
    color: #3b82f6;
    transform: translateY(-1px);
}

.video-stat i {
    font-size: 1.125rem;
    opacity: 0.9;
}

/* Action Buttons */
.video-actions {
    display: flex;
    gap: 1rem;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.dark .video-actions {
    border-color: rgba(255, 255, 255, 0.05);
}

.action-btn {
    flex: 1;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.625rem;
    padding: 0.75rem;
    border: none;
    border-radius: 12px;
    background: #f1f5f9;
    color: #475569;
    font-size: 0.938rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dark .action-btn {
    background: rgba(51, 65, 85, 0.5);
    color: #94a3b8;
}

.action-btn:hover {
    background: #e2e8f0;
    color: #0f172a;
    transform: translateY(-2px);
}

.dark .action-btn:hover {
    background: #475569;
    color: #f1f5f9;
}

.action-btn.liked {
    background: #fee2e2;
    color: #ef4444;
}

.dark .action-btn.liked {
    background: rgba(153, 27, 27, 0.5);
    color: #fca5a5;
}

/* Tags */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.625rem;
    margin-top: 0.5rem;
}

.tag {
    background: #f1f5f9;
    color: #475569;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.813rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dark .tag {
    background: rgba(51, 65, 85, 0.5);
    color: #94a3b8;
}

.tag:hover {
    background: #e2e8f0;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.dark .tag:hover {
    background: #475569;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

/* Sidebar */
.sidebar {
    position: sticky;
    top: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.sidebar-container {
    background: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.dark .sidebar-container {
    background: rgba(30, 41, 59, 0.8);
    border-color: rgba(255, 255, 255, 0.05);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

.sidebar-header {
    padding: 1.25rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.dark .sidebar-header {
    border-color: rgba(255, 255, 255, 0.05);
}

.sidebar-title {
    font-size: 1.125rem;
    font-weight: 600;
    background: linear-gradient(135deg, #0f172a, #334155);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin: 0;
}

.dark .sidebar-title {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.sidebar-content {
    padding: 1.25rem;
}

/* Responsive Design */
@media (max-width: 1536px) {
    .video-feed {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
}

@media (max-width: 1280px) {
    .video-feed {
        grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    }
}

@media (max-width: 1024px) {
    .video-feed-container {
        padding: 1.5rem;
    }
    
    .sidebar {
        position: static;
        margin-top: 2rem;
    }
}

@media (max-width: 768px) {
    .video-feed {
        grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
        gap: 1.5rem;
    }
    
    .video-info {
        padding: 1.25rem;
    }
    
    .creator-avatar {
        width: 42px;
        height: 42px;
    }
}

@media (max-width: 640px) {
    .video-feed-container {
        padding: 1rem;
    }
    
    .video-feed {
        grid-template-columns: 1fr;
        max-width: 400px;
        margin: 0 auto;
    }
    
    .video-title {
        font-size: 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.4s ease;
}

@keyframes slideUp {
    from { 
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.slide-up {
    animation: slideUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
}

/* Loading States */
.skeleton {
    background: linear-gradient(
        90deg,
        #f1f5f9 0%,
        #e2e8f0 50%,
        #f1f5f9 100%
    );
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.dark .skeleton {
    background: linear-gradient(
        90deg,
        #334155 0%,
        #475569 50%,
        #334155 100%
    );
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: #ffffff;
    border-radius: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark .empty-state {
    background: #1e293b;
    border-color: rgba(255, 255, 255, 0.1);
}

.empty-state i {
    font-size: 3rem;
    color: #64748b;
    margin-bottom: 1.5rem;
}

.empty-state h3 {
    color: #0f172a;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.dark .empty-state h3 {
    color: #f1f5f9;
}

.empty-state p {
    color: #64748b;
    margin-bottom: 1.5rem;
    font-size: 0.938rem;
}

.dark .empty-state p {
    color: #94a3b8;
}

/* Comments Section */
.comments-section {
    margin-top: 2rem;
}

.comment-form {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.comment-input {
    flex: 1;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 0.75rem;
    resize: none;
    transition: border-color 0.2s ease;
}

.comment-input:focus {
    border-color: #3b82f6;
    outline: none;
}

.comment-item {
    display: flex;
    gap: 1rem;
    padding: 1rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.comment-content {
    flex: 1;
}

/* Loading Skeleton */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.dark .skeleton {
    background: linear-gradient(90deg, #2d3748 25%, #1a202c 50%, #2d3748 75%);
    background-size: 200% 100%;
}

/* Video Thumbnail */
.video-thumbnail {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
}

.video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Loading States */
.video-card.loading {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

/* Video Upload/Create Page */
.upload-container {
    max-width: 800px;
    margin: 2rem auto;
    background: linear-gradient(145deg, #ffffff, #f8fafc);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 2.5rem;
    position: relative;
    overflow: hidden;
}

.dark .upload-container {
    background: linear-gradient(145deg, #1e293b, #0f172a);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.upload-header {
    text-align: center;
    margin-bottom: 2.5rem;
    position: relative;
}

.upload-header h1 {
    font-size: 2.25rem;
    font-weight: 800;
    background: linear-gradient(135deg, #3b82f6, #60a5fa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
    letter-spacing: -0.025em;
}

.dark .upload-header h1 {
    background: linear-gradient(135deg, #60a5fa, #93c5fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.upload-header p {
    color: #64748b;
    font-size: 1.1rem;
    max-width: 500px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Upload Area */
.upload-area {
    border: 2px dashed #3b82f6;
    border-radius: 16px;
    padding: 3rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 2.5rem;
    background: rgba(59, 130, 246, 0.02);
    position: relative;
}

.dark .upload-area {
    border-color: #60a5fa;
    background: rgba(96, 165, 250, 0.05);
}

.upload-area:hover {
    transform: scale(1.02);
    border-color: #2563eb;
    background: rgba(59, 130, 246, 0.05);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15);
}

.dark .upload-area:hover {
    border-color: #3b82f6;
    background: rgba(96, 165, 250, 0.1);
    box-shadow: 0 8px 24px rgba(96, 165, 250, 0.2);
}

.upload-area.dragging {
    background: rgba(59, 130, 246, 0.1);
    border-color: #2563eb;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3.5rem;
    color: #3b82f6;
    margin-bottom: 1.5rem;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-area:hover .upload-icon {
    transform: translateY(-5px);
}

.upload-text {
    font-size: 1.25rem;
    font-weight: 600;
    background: linear-gradient(135deg, #1e40af, #3b82f6);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 0.75rem;
}

.dark .upload-text {
    background: linear-gradient(135deg, #60a5fa, #93c5fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.upload-hint {
    font-size: 0.95rem;
    color: #64748b;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Form Fields */
.form-group {
    margin-bottom: 1.75rem;
}

.form-label {
    display: block;
    font-size: 0.95rem;
    font-weight: 600;
    color: #0f172a;
    margin-bottom: 0.75rem;
    letter-spacing: 0.025em;
}

.dark .form-label {
    color: #f1f5f9;
}

.form-control {
    width: 100%;
    padding: 0.875rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    color: #0f172a;
    background: #ffffff;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.dark .form-control {
    background: rgba(30, 41, 59, 0.8);
    border-color: #334155;
    color: #f1f5f9;
}

.form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

.dark .form-control:focus {
    border-color: #60a5fa;
    box-shadow: 0 0 0 4px rgba(96, 165, 250, 0.15);
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
    line-height: 1.6;
}

/* Tags Input */
.tags-input {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    background: #ffffff;
    min-height: 56px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.dark .tags-input {
    background: rgba(30, 41, 59, 0.8);
    border-color: #334155;
}

.tag-item {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #f1f5f9;
    color: #1e293b;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.dark .tag-item {
    background: #334155;
    color: #f1f5f9;
}

.tag-item:hover {
    background: #e2e8f0;
    transform: translateY(-1px);
}

.dark .tag-item:hover {
    background: #475569;
}

/* Submit Button */
.submit-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: #ffffff;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: 2rem;
    position: relative;
    overflow: hidden;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.25);
}

.submit-btn:active {
    transform: translateY(0);
}

.dark .submit-btn {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
}

.dark .submit-btn:hover {
    box-shadow: 0 8px 20px rgba(96, 165, 250, 0.25);
}

/* Privacy Options */
.privacy-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.privacy-option {
    padding: 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #ffffff;
    position: relative;
    overflow: hidden;
}

.dark .privacy-option {
    background: rgba(30, 41, 59, 0.8);
    border-color: #334155;
}

.privacy-option:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.dark .privacy-option:hover {
    border-color: #60a5fa;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.privacy-option.selected {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.dark .privacy-option.selected {
    border-color: #60a5fa;
    background: rgba(96, 165, 250, 0.1);
}

.privacy-option-icon {
    font-size: 1.5rem;
    color: #3b82f6;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.privacy-option:hover .privacy-option-icon {
    transform: scale(1.1);
}

.privacy-option-title {
    font-weight: 600;
    color: #0f172a;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.dark .privacy-option-title {
    color: #f1f5f9;
}

.privacy-option-description {
    font-size: 0.9rem;
    color: #64748b;
    line-height: 1.5;
}

.dark .privacy-option-description {
    color: #94a3b8;
}

/* Progress Bar */
.upload-progress {
    margin: 2rem 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-progress.active {
    opacity: 1;
}

.progress-bar {
    height: 6px;
    background: #e2e8f0;
    border-radius: 999px;
    overflow: hidden;
    position: relative;
}

.dark .progress-bar {
    background: #334155;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 999px;
}

.dark .progress-fill {
    background: linear-gradient(135deg, #60a5fa, #3b82f6);
}

.progress-text {
    font-size: 0.9rem;
    color: #64748b;
    margin-top: 0.75rem;
    text-align: center;
    font-weight: 500;
}

.dark .progress-text {
    color: #94a3b8;
} 