<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\VideoController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\LikeController;
use App\Http\Controllers\ShareController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

Route::get('/dashboard', function () {
    $user = auth()->user();
    $user->loadCount(['videos', 'followers', 'following']);
    $user->total_views = $user->videos()->sum('views');
    $recentVideos = $user->videos()
        ->withCount(['likes', 'comments'])
        ->latest()
        ->take(6)
        ->get();
    
    return view('dashboard', compact('user', 'recentVideos'));
})->middleware(['auth', 'verified'])->name('dashboard');

// Video Routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Video CRUD
    Route::get('/videos', [VideoController::class, 'index'])->name('videos.index');
    Route::get('/videos/create', [VideoController::class, 'create'])->name('videos.create');
    Route::post('/videos', [VideoController::class, 'store'])->name('videos.store');
    Route::get('/videos/{video}', [VideoController::class, 'show'])->name('videos.show');
    Route::get('/videos/{video}/edit', [VideoController::class, 'edit'])->name('videos.edit');
    Route::put('/videos/{video}', [VideoController::class, 'update'])->name('videos.update');
    Route::delete('/videos/{video}', [VideoController::class, 'destroy'])->name('videos.destroy');
    
    // Video Statistics
    Route::get('/videos/{video}/statistics', [VideoController::class, 'statistics'])->name('videos.statistics');
    Route::post('/videos/{video}/watch-time', [VideoController::class, 'trackWatchTime'])->name('videos.watch-time');
    
    // Video Chapters
    Route::get('/videos/{video}/chapters', [VideoController::class, 'chapters'])->name('videos.chapters');
    Route::post('/videos/{video}/chapters', [VideoController::class, 'storeChapter'])->name('videos.chapters.store');
    Route::put('/videos/{video}/chapters/{chapter}', [VideoController::class, 'updateChapter'])->name('videos.chapters.update');
    Route::delete('/videos/{video}/chapters/{chapter}', [VideoController::class, 'deleteChapter'])->name('videos.chapters.delete');
    
    // Comments
    Route::post('/comments', [CommentController::class, 'store'])->name('comments.store');
    Route::put('/comments/{comment}', [CommentController::class, 'update'])->name('comments.update');
    Route::delete('/comments/{comment}', [CommentController::class, 'destroy'])->name('comments.destroy');
    Route::post('/comments/{comment}/like', [CommentController::class, 'like'])->name('comments.like');
    Route::post('/comments/{comment}/reply', [CommentController::class, 'reply'])->name('comments.reply');
    
    // Likes
    Route::post('/videos/{video}/like', [LikeController::class, 'toggle'])->name('videos.like');
    
    // Shares
    Route::post('/videos/{video}/share', [ShareController::class, 'share'])->name('videos.share');
    Route::get('/videos/{video}/share-link', [ShareController::class, 'getShareLink'])->name('videos.share-link');
});

// Public video routes
Route::get('/videos/{video}/embed', [VideoController::class, 'embed'])->name('videos.embed');
Route::get('/videos/{video}/thumbnail', [VideoController::class, 'thumbnail'])->name('videos.thumbnail');

// Profile routes
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

require __DIR__.'/auth.php';
