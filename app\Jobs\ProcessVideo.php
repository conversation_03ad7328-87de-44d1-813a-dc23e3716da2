<?php

namespace App\Jobs;

use App\Models\Video;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use ProtoneMedia\LaravelFFMpeg\Support\FFmpeg;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use FFMpeg\Coordinate\TimeCode;

class ProcessVideo implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $video;

    /**
     * Create a new job instance.
     */
    public function __construct(Video $video)
    {
        $this->video = $video;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Get video duration
            $duration = FFmpeg::fromDisk('public')
                ->open($this->video->video_path)
                ->getDurationInSeconds();

            // Generate thumbnail
            $thumbnailPath = 'thumbnails/' . pathinfo($this->video->video_path, PATHINFO_FILENAME) . '.jpg';
            
            FFmpeg::fromDisk('public')
                ->open($this->video->video_path)
                ->frame(TimeCode::fromSeconds(1))
                ->save($thumbnailPath);

            // Update video record
            $this->video->update([
                'duration' => $duration,
                'thumbnail_path' => $thumbnailPath,
                'status' => 'published'
            ]);

            // Calculate initial earnings based on video length
            $baseEarnings = $this->calculateBaseEarnings($duration);
            $this->video->update(['earnings' => $baseEarnings]);

        } catch (\Exception $e) {
            $this->video->update(['status' => 'failed']);
            throw $e;
        }
    }

    protected function calculateBaseEarnings($duration)
    {
        // Base rate per second (example: $0.01 per second)
        $baseRate = 0.01;
        return $duration * $baseRate;
    }

    public function failed(\Throwable $exception)
    {
        $this->video->update(['status' => 'failed']);
        Log::error('Video processing failed: ' . $exception->getMessage());
    }
}
