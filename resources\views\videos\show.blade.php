@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row">
        <!-- Main Video -->
        <div class="col-lg-8">
            <div class="video-player-container mb-4">
                <video 
                    src="{{ asset('storage/' . $video->video_path) }}"
                    controls
                    autoplay
                    data-video-id="{{ $video->id }}"
                    class="w-100"
                ></video>
            </div>
            
            <!-- Video Info -->
            <div class="video-info bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4 mb-4">
                <h1 class="video-title">{{ $video->title }}</h1>
                
                <div class="creator-info">
                    <img src="{{ $video->user->profile_photo_url }}" 
                         class="creator-avatar"
                         alt="{{ $video->user->name }}">
                    <div class="flex-grow-1">
                        <h5 class="mb-0">{{ $video->user->name }}</h5>
                        <div class="d-flex align-items-center text-muted">
                            <span>{{ $video->created_at->diffForHumans() }}</span>
                            <span class="mx-2">•</span>
                            <span>{{ number_format($video->views_count) }} views</span>
                        </div>
                    </div>
                    @if($video->user_id !== auth()->id())
                    <button class="btn btn-primary rounded-pill follow-button" 
                            data-user-id="{{ $video->user_id }}">
                        {{ $video->user->is_followed ? 'Following' : 'Follow' }}
                    </button>
                    @endif
                </div>
                
                <div class="video-controls border-top border-bottom py-3 my-3">
                    <div class="d-flex gap-4">
                        <button class="video-control-btn {{ $video->is_liked ? 'liked' : '' }}" 
                                data-video-id="{{ $video->id }}">
                            <i class="fas fa-heart"></i>
                            <span class="ms-2">{{ number_format($video->likes_count) }}</span>
                        </button>
                        <button class="video-control-btn" 
                                data-video-id="{{ $video->id }}">
                            <i class="fas fa-share"></i>
                            <span class="ms-2">{{ number_format($video->shares_count) }}</span>
                        </button>
                        <div class="d-flex align-items-center text-muted">
                            <i class="fas fa-eye me-2"></i>
                            <span>{{ number_format($video->views_count) }} views</span>
                        </div>
                    </div>
                </div>
                
                <div class="video-description">
                    <p class="mb-3">{{ $video->description }}</p>
                    <div class="d-flex gap-2">
                        @foreach($video->tags as $tag)
                        <span class="badge bg-primary">{{ $tag }}</span>
                        @endforeach
                    </div>
                </div>
            </div>
            
            <!-- Comments Section -->
            <div class="comments-section bg-white dark:bg-gray-800 rounded-lg shadow-sm p-4">
                <h5 class="mb-4">Comments ({{ number_format($video->comments_count) }})</h5>
                
                <!-- Comment Form -->
                <form class="comment-form">
                    <div class="d-flex gap-3">
                        <img src="{{ auth()->user()->profile_photo_url }}" 
                             class="creator-avatar"
                             alt="{{ auth()->user()->name }}">
                        <div class="flex-grow-1">
                            <textarea name="content" 
                                      class="comment-input" 
                                      rows="2" 
                                      placeholder="Add a comment..."
                                      required></textarea>
                            <div class="d-flex justify-content-end mt-2">
                                <button type="submit" class="btn btn-primary btn-sm px-4">
                                    Post Comment
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
                
                <!-- Comments List -->
                <div class="comments-list mt-4">
                    @foreach($comments as $comment)
                    <div class="comment-item">
                        <img src="{{ $comment->user->profile_photo_url }}" 
                             class="creator-avatar"
                             alt="{{ $comment->user->name }}">
                        <div class="comment-content">
                            <div class="d-flex align-items-center mb-1">
                                <h6 class="mb-0 me-2">{{ $comment->user->name }}</h6>
                                <small class="text-muted">{{ $comment->created_at->diffForHumans() }}</small>
                            </div>
                            <p class="mb-2">{{ $comment->content }}</p>
                            <div class="d-flex gap-3">
                                <button class="btn btn-link btn-sm p-0 text-muted" 
                                        onclick="likeComment({{ $comment->id }})">
                                    <i class="fas fa-heart {{ $comment->is_liked ? 'text-danger' : '' }}"></i>
                                    <span class="ms-1">{{ number_format($comment->likes_count) }}</span>
                                </button>
                                <button class="btn btn-link btn-sm p-0 text-muted" 
                                        onclick="replyComment({{ $comment->id }})">
                                    <i class="fas fa-reply me-1"></i>Reply
                                </button>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $comments->links() }}
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <div class="sticky-top" style="top: 20px;">
                <!-- More Videos from Creator -->
                <div class="sidebar-card mb-4">
                    <div class="sidebar-card-header">
                        <h5 class="mb-0">More from {{ $video->user->name }}</h5>
                    </div>
                    <div class="p-3">
                        @foreach($userVideos as $userVideo)
                        <a href="{{ route('videos.show', $userVideo) }}" class="text-decoration-none">
                            <div class="d-flex gap-3 mb-3">
                                <div class="video-thumbnail" style="width: 120px;">
                                    <img src="{{ Storage::url($userVideo->thumbnail_path) }}" 
                                         alt="Thumbnail">
                                    <span class="video-duration">{{ gmdate('i:s', $userVideo->duration) }}</span>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-body">{{ Str::limit($userVideo->title, 50) }}</h6>
                                    <div class="d-flex align-items-center text-muted">
                                        <i class="fas fa-eye me-1"></i>
                                        <small>{{ number_format($userVideo->views_count) }} views</small>
                                    </div>
                                </div>
                            </div>
                        </a>
                        @endforeach
                    </div>
                </div>
                
                <!-- Related Videos -->
                <div class="sidebar-card">
                    <div class="sidebar-card-header">
                        <h5 class="mb-0">Related Videos</h5>
                    </div>
                    <div class="p-3">
                        @foreach($relatedVideos as $related)
                        <a href="{{ route('videos.show', $related) }}" class="text-decoration-none">
                            <div class="d-flex gap-3 mb-3">
                                <div class="video-thumbnail" style="width: 120px;">
                                    <img src="{{ Storage::url($related->thumbnail_path) }}" 
                                         alt="Thumbnail">
                                    <span class="video-duration">{{ gmdate('i:s', $related->duration) }}</span>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-body">{{ Str::limit($related->title, 50) }}</h6>
                                    <div class="d-flex align-items-center text-muted">
                                        <i class="fas fa-eye me-1"></i>
                                        <small>{{ number_format($related->views_count) }} views</small>
                                        <span class="mx-2">•</span>
                                        <small>{{ $related->created_at->diffForHumans() }}</small>
                                    </div>
                                </div>
                            </div>
                        </a>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function likeComment(commentId) {
    // Implement comment like functionality
}

function replyComment(commentId) {
    // Implement comment reply functionality
}

// Track video view
document.querySelector('video').addEventListener('timeupdate', function() {
    if (this.currentTime > 5 && !this.viewTracked) {
        this.viewTracked = true;
        fetch(`/videos/${this.dataset.videoId}/view`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            }
        });
    }
});
</script>
@endpush

@vite(['resources/css/video.css'])
@endsection
