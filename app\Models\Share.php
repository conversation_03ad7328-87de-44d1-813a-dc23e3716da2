<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Share extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'video_id',
        'platform',
        'share_url'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function video()
    {
        return $this->belongsTo(Video::class);
    }

    public static function createShare($userId, $videoId, $platform, $shareUrl = null)
    {
        return static::create([
            'user_id' => $userId,
            'video_id' => $videoId,
            'platform' => $platform,
            'share_url' => $shareUrl
        ]);
    }
}
