<?php

namespace App\Http\Controllers;

use App\Models\Video;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class ShareController extends Controller
{
    public function share(Request $request, Video $video)
    {
        $platform = $request->input('platform');
        $url = route('videos.show', $video);
        
        $shareUrls = [
            'facebook' => "https://www.facebook.com/sharer/sharer.php?u=" . urlencode($url),
            'twitter' => "https://twitter.com/intent/tweet?url=" . urlencode($url) . "&text=" . urlencode($video->title),
            'whatsapp' => "https://wa.me/?text=" . urlencode($video->title . " " . $url),
            'telegram' => "https://t.me/share/url?url=" . urlencode($url) . "&text=" . urlencode($video->title),
            'email' => "mailto:?subject=" . urlencode($video->title) . "&body=" . urlencode($url)
        ];
        
        if (isset($shareUrls[$platform])) {
            return response()->json([
                'share_url' => $shareUrls[$platform]
            ]);
        }
        
        return response()->json([
            'error' => 'Invalid platform'
        ], 400);
    }
    
    public function getShareLink(Request $request, Video $video)
    {
        $shareToken = Str::random(32);
        $video->update(['share_token' => $shareToken]);
        
        $shareUrl = route('videos.show', [
            'video' => $video,
            'token' => $shareToken
        ]);
        
        return response()->json([
            'share_url' => $shareUrl
        ]);
    }
}
