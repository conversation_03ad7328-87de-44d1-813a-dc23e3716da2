// Video Feed Interactions
class VideoFeed {
    constructor() {
        this.videos = document.querySelectorAll('video');
        this.currentPage = 1;
        this.loading = false;
        this.hasMore = true;
        this.observer = null;
        
        this.init();
    }
    
    init() {
        this.initVideoPlayers();
        this.initInfiniteScroll();
    }
    
    initVideoPlayers() {
        this.videos.forEach(video => {
            // Click to play/pause
            video.addEventListener('click', () => {
                if (video.paused) {
                    video.play();
                } else {
                    video.pause();
                }
            });
            
            // Pause other videos when one starts playing
            video.addEventListener('play', () => {
                this.videos.forEach(otherVideo => {
                    if (otherVideo !== video && !otherVideo.paused) {
                        otherVideo.pause();
                    }
                });
            });
            
            // Handle video loading
            video.addEventListener('loadedmetadata', () => {
                video.classList.add('loaded');
            });
        });
    }
    
    initInfiniteScroll() {
        this.observer = new IntersectionObserver(
            (entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.loading && this.hasMore) {
                        this.loadMoreVideos();
                    }
                });
            },
            { threshold: 0.5 }
        );
        
        // Observe the last video
        if (this.videos.length > 0) {
            this.observer.observe(this.videos[this.videos.length - 1].closest('.video-container'));
        }
    }
    
    async loadMoreVideos() {
        this.loading = true;
        try {
            const response = await fetch(`/videos?page=${this.currentPage + 1}`);
            const data = await response.json();
            
            if (data.videos.length > 0) {
                this.appendVideos(data.videos);
                this.currentPage++;
            } else {
                this.hasMore = false;
            }
        } catch (error) {
            console.error('Error loading more videos:', error);
        } finally {
            this.loading = false;
        }
    }
    
    appendVideos(videos) {
        const videoFeed = document.querySelector('.video-feed');
        videos.forEach(video => {
            const videoElement = this.createVideoElement(video);
            videoFeed.appendChild(videoElement);
            this.observer.observe(videoElement);
        });
    }
    
    createVideoElement(video) {
        // Create video element HTML
        const template = `
            <div class="video-container mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="video-wrapper position-relative">
                        <video 
                            class="w-100" 
                            src="${video.video_path}"
                            loop
                            preload="metadata"
                            playsinline
                        ></video>
                        <!-- Video Controls -->
                        <div class="video-controls position-absolute bottom-0 start-0 w-100 p-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <img src="${video.user.profile_photo_url}" 
                                         class="rounded-circle me-2" 
                                         width="40" 
                                         height="40" 
                                         alt="${video.user.name}">
                                    <div>
                                        <h6 class="mb-0 text-white">${video.user.name}</h6>
                                        <small class="text-white-50">${video.created_at}</small>
                                    </div>
                                </div>
                                <div class="d-flex gap-3">
                                    <button class="btn btn-light btn-sm rounded-circle" onclick="videoInteractions.likeVideo(${video.id})">
                                        <i class="fas fa-heart"></i>
                                        <span class="ms-1">${video.likes_count}</span>
                                    </button>
                                    <button class="btn btn-light btn-sm rounded-circle" onclick="videoInteractions.shareVideo(${video.id})">
                                        <i class="fas fa-share"></i>
                                        <span class="ms-1">${video.shares_count}</span>
                                    </button>
                                    <button class="btn btn-light btn-sm rounded-circle" onclick="videoInteractions.commentVideo(${video.id})">
                                        <i class="fas fa-comment"></i>
                                        <span class="ms-1">${video.comments_count}</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <p class="card-text">${video.description}</p>
                        <div class="d-flex gap-2">
                            ${video.tags.map(tag => `<span class="badge bg-primary">${tag}</span>`).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        const temp = document.createElement('div');
        temp.innerHTML = template;
        return temp.firstElementChild;
    }
}

// Video Interactions
class VideoInteractions {
    constructor() {
        this.csrfToken = document.querySelector('meta[name="csrf-token"]').content;
    }
    
    async likeVideo(videoId) {
        try {
            const response = await fetch(`/videos/${videoId}/like`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            this.updateLikeButton(videoId, data.is_liked, data.likes_count);
        } catch (error) {
            console.error('Error liking video:', error);
        }
    }
    
    async shareVideo(videoId) {
        try {
            const response = await fetch(`/videos/${videoId}/share`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            this.showShareOptions(data.share_url);
        } catch (error) {
            console.error('Error sharing video:', error);
        }
    }
    
    async commentVideo(videoId) {
        // Show comment modal
        const modal = new bootstrap.Modal(document.getElementById('commentModal'));
        modal.show();
        
        // Load comments
        await this.loadComments(videoId);
    }
    
    async loadComments(videoId) {
        try {
            const response = await fetch(`/videos/${videoId}/comments`);
            const data = await response.json();
            this.renderComments(data.comments);
        } catch (error) {
            console.error('Error loading comments:', error);
        }
    }
    
    async submitComment(videoId, content) {
        try {
            const response = await fetch('/comments', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    video_id: videoId,
                    content: content
                })
            });
            
            const data = await response.json();
            this.appendComment(data.comment);
        } catch (error) {
            console.error('Error submitting comment:', error);
        }
    }
    
    updateLikeButton(videoId, isLiked, likesCount) {
        const button = document.querySelector(`button[onclick="videoInteractions.likeVideo(${videoId})"]`);
        const icon = button.querySelector('i');
        const count = button.querySelector('span');
        
        icon.classList.toggle('text-danger', isLiked);
        count.textContent = likesCount;
    }
    
    showShareOptions(shareUrl) {
        // Create share options modal
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Share Video</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Share Link</label>
                            <div class="input-group">
                                <input type="text" class="form-control" value="${shareUrl}" readonly>
                                <button class="btn btn-outline-primary" onclick="navigator.clipboard.writeText('${shareUrl}')">
                                    Copy
                                </button>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary flex-grow-1" onclick="window.open('https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}')">
                                <i class="fab fa-facebook"></i> Facebook
                            </button>
                            <button class="btn btn-info flex-grow-1 text-white" onclick="window.open('https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}')">
                                <i class="fab fa-twitter"></i> Twitter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
        
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }
    
    renderComments(comments) {
        const container = document.querySelector('.comments-list');
        container.innerHTML = comments.map(comment => this.createCommentElement(comment)).join('');
    }
    
    createCommentElement(comment) {
        return `
            <div class="comment-item mb-4">
                <div class="d-flex gap-3">
                    <img src="${comment.user.profile_photo_url}" 
                         class="rounded-circle" 
                         width="40" 
                         height="40" 
                         alt="${comment.user.name}">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <h6 class="mb-0 me-2">${comment.user.name}</h6>
                            <small class="text-muted">${comment.created_at}</small>
                        </div>
                        <p class="mb-1">${comment.content}</p>
                        <div class="d-flex gap-3">
                            <button class="btn btn-link btn-sm p-0 text-muted" onclick="videoInteractions.likeComment(${comment.id})">
                                <i class="fas fa-heart ${comment.is_liked ? 'text-danger' : ''}"></i>
                                <span class="ms-1">${comment.likes_count}</span>
                            </button>
                            <button class="btn btn-link btn-sm p-0 text-muted" onclick="videoInteractions.replyComment(${comment.id})">
                                Reply
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    appendComment(comment) {
        const container = document.querySelector('.comments-list');
        container.insertAdjacentHTML('afterbegin', this.createCommentElement(comment));
    }
}

// Initialize
const videoFeed = new VideoFeed();
const videoInteractions = new VideoInteractions(); 