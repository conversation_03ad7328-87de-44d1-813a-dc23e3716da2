<?php

namespace App\Http\Controllers;

use App\Models\Comment;
use App\Models\Video;
use App\Models\Like;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CommentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index(Video $video)
    {
        $comments = $video->comments()
            ->with('user')
            ->whereNull('parent_id')
            ->latest()
            ->paginate(10);

        return response()->json($comments);
    }

    public function store(Request $request, Video $video)
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:1000',
            'parent_id' => 'nullable|exists:comments,id'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $comment = Comment::create([
            'user_id' => auth()->id(),
            'video_id' => $video->id,
            'content' => $request->content,
            'parent_id' => $request->parent_id
        ]);

        $comment->load('user');

        return response()->json($comment, 201);
    }

    public function update(Request $request, Comment $comment)
    {
        $this->authorize('update', $comment);

        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:1000'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $comment->update([
            'content' => $request->content,
            'is_edited' => true
        ]);

        return response()->json($comment);
    }

    public function destroy(Comment $comment)
    {
        $this->authorize('delete', $comment);

        $comment->delete();

        return response()->json(null, 204);
    }

    public function like(Comment $comment)
    {
        $liked = Like::toggle(auth()->id(), $comment->id);
        
        if ($liked) {
            $comment->incrementLikesCount();
        } else {
            $comment->decrementLikesCount();
        }

        return response()->json([
            'likes_count' => $comment->likes_count,
            'is_liked' => $liked
        ]);
    }
}
