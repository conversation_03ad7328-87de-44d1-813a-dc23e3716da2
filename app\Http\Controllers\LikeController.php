<?php

namespace App\Http\Controllers;

use App\Models\Video;
use App\Models\Like;
use Illuminate\Http\Request;

class LikeController extends Controller
{
    public function toggle(Request $request, Video $video)
    {
        $like = Like::where('user_id', auth()->id())
                    ->where('video_id', $video->id)
                    ->first();
        
        if ($like) {
            $like->delete();
            $isLiked = false;
        } else {
            Like::create([
                'user_id' => auth()->id(),
                'video_id' => $video->id
            ]);
            $isLiked = true;
        }
        
        return response()->json([
            'is_liked' => $isLiked,
            'likes_count' => $video->likes()->count()
        ]);
    }
}
