class VideoPlayer {
    constructor() {
        this.video = document.querySelector('.video-container video');
        this.csrfToken = document.querySelector('meta[name="csrf-token"]').content;
        this.videoId = this.video.dataset.videoId;
        this.currentQuality = 'auto';
        this.playbackSpeed = 1;
        this.replyingTo = null;
        this.watchTime = 0;
        this.lastWatchTimeUpdate = Date.now();
        this.chapters = [];
        this.autoplaySettings = {
            enabled: false,
            nextVideo: true,
            muted: true
        };
        
        this.init();
    }
    
    init() {
        this.initVideoPlayer();
        this.initInteractions();
        this.initVideoControls();
        this.initChapters();
        this.initAutoplaySettings();
        this.trackVideoView();
        this.startWatchTimeTracking();
    }
    
    initVideoPlayer() {
        // Add custom controls
        this.video.addEventListener('click', () => {
            if (this.video.paused) {
                this.video.play();
            } else {
                this.video.pause();
            }
        });
        
        // Handle keyboard controls
        document.addEventListener('keydown', (e) => {
            switch(e.code) {
                case 'Space':
                    e.preventDefault();
                    if (this.video.paused) {
                        this.video.play();
                    } else {
                        this.video.pause();
                    }
                    break;
                case 'ArrowLeft':
                    this.video.currentTime -= 5;
                    break;
                case 'ArrowRight':
                    this.video.currentTime += 5;
                    break;
                case 'ArrowUp':
                    this.video.volume = Math.min(1, this.video.volume + 0.1);
                    break;
                case 'ArrowDown':
                    this.video.volume = Math.max(0, this.video.volume - 0.1);
                    break;
                case 'KeyM':
                    this.video.muted = !this.video.muted;
                    break;
            }
        });
        
        // Handle video loading
        this.video.addEventListener('loadedmetadata', () => {
            this.video.classList.add('loaded');
            this.initQualitySelector();
        });
        
        // Handle video errors
        this.video.addEventListener('error', () => {
            console.error('Error loading video');
            alert('Error loading video. Please try again.');
        });
    }
    
    initInteractions() {
        // Like button
        const likeButton = document.querySelector('.like-button');
        if (likeButton) {
            likeButton.addEventListener('click', () => this.likeVideo());
        }
        
        // Share button
        const shareButton = document.querySelector('.share-button');
        if (shareButton) {
            shareButton.addEventListener('click', () => this.shareVideo());
        }
        
        // Follow button
        const followButton = document.querySelector('.follow-button');
        if (followButton) {
            followButton.addEventListener('click', () => this.followUser());
        }
        
        // Comment form
        const commentForm = document.querySelector('.comment-form');
        if (commentForm) {
            commentForm.addEventListener('submit', (e) => this.submitComment(e));
        }
    }
    
    initVideoControls() {
        // Create custom controls container
        const controlsContainer = document.createElement('div');
        controlsContainer.className = 'video-controls position-absolute bottom-0 start-0 w-100 p-3';
        controlsContainer.innerHTML = `
            <div class="d-flex align-items-center gap-3">
                <button class="btn btn-light btn-sm rounded-circle play-pause">
                    <i class="fas fa-play"></i>
                </button>
                <div class="progress flex-grow-1" style="height: 4px;">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <span class="time">0:00 / 0:00</span>
                    <div class="dropdown">
                        <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            ${this.playbackSpeed}x
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-speed="0.5">0.5x</a></li>
                            <li><a class="dropdown-item" href="#" data-speed="1">1x</a></li>
                            <li><a class="dropdown-item" href="#" data-speed="1.5">1.5x</a></li>
                            <li><a class="dropdown-item" href="#" data-speed="2">2x</a></li>
                        </ul>
                    </div>
                    <button class="btn btn-light btn-sm rounded-circle mute">
                        <i class="fas fa-volume-up"></i>
                    </button>
                </div>
            </div>
        `;
        
        this.video.parentElement.appendChild(controlsContainer);
        
        // Initialize custom controls
        this.initCustomControls(controlsContainer);
        
        // Add statistics button
        const statsBtn = document.createElement('button');
        statsBtn.className = 'btn btn-light btn-sm rounded-circle stats-btn';
        statsBtn.innerHTML = '<i class="fas fa-chart-bar"></i>';
        statsBtn.setAttribute('data-bs-toggle', 'dropdown');
        
        const statsMenu = document.createElement('ul');
        statsMenu.className = 'dropdown-menu stats-menu';
        statsMenu.innerHTML = `
            <li><h6 class="dropdown-header">Video Statistics</h6></li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <div class="px-3 py-2">
                    <div class="d-flex justify-content-between mb-1">
                        <span>Watch Time:</span>
                        <span class="watch-time">0:00</span>
                    </div>
                    <div class="d-flex justify-content-between mb-1">
                        <span>Views:</span>
                        <span class="views-count">0</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Likes:</span>
                        <span class="likes-count">0</span>
                    </div>
                </div>
            </li>
        `;
        
        statsBtn.appendChild(statsMenu);
        this.video.parentElement.querySelector('.video-controls .d-flex').appendChild(statsBtn);
        
        // Load initial statistics
        this.loadVideoStatistics();
    }
    
    initCustomControls(container) {
        const playPauseBtn = container.querySelector('.play-pause');
        const progressBar = container.querySelector('.progress-bar');
        const timeDisplay = container.querySelector('.time');
        const muteBtn = container.querySelector('.mute');
        
        // Play/Pause
        playPauseBtn.addEventListener('click', () => {
            if (this.video.paused) {
                this.video.play();
                playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
            } else {
                this.video.pause();
                playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
            }
        });
        
        // Progress bar
        this.video.addEventListener('timeupdate', () => {
            const progress = (this.video.currentTime / this.video.duration) * 100;
            progressBar.style.width = `${progress}%`;
            timeDisplay.textContent = `${this.formatTime(this.video.currentTime)} / ${this.formatTime(this.video.duration)}`;
        });
        
        // Click on progress bar
        container.querySelector('.progress').addEventListener('click', (e) => {
            const rect = e.target.getBoundingClientRect();
            const pos = (e.clientX - rect.left) / rect.width;
            this.video.currentTime = pos * this.video.duration;
        });
        
        // Mute
        muteBtn.addEventListener('click', () => {
            this.video.muted = !this.video.muted;
            muteBtn.innerHTML = this.video.muted ? 
                '<i class="fas fa-volume-mute"></i>' : 
                '<i class="fas fa-volume-up"></i>';
        });
        
        // Playback speed
        container.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const speed = parseFloat(e.target.dataset.speed);
                this.video.playbackRate = speed;
                this.playbackSpeed = speed;
                container.querySelector('.dropdown-toggle').textContent = `${speed}x`;
            });
        });
    }
    
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        seconds = Math.floor(seconds % 60);
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
    
    initQualitySelector() {
        // Add quality selector to video controls
        const qualityBtn = document.createElement('button');
        qualityBtn.className = 'btn btn-light btn-sm rounded-circle quality-selector';
        qualityBtn.innerHTML = '<i class="fas fa-cog"></i>';
        qualityBtn.setAttribute('data-bs-toggle', 'dropdown');
        
        const qualityMenu = document.createElement('ul');
        qualityMenu.className = 'dropdown-menu';
        qualityMenu.innerHTML = `
            <li><a class="dropdown-item" href="#" data-quality="auto">Auto</a></li>
            <li><a class="dropdown-item" href="#" data-quality="1080p">1080p</a></li>
            <li><a class="dropdown-item" href="#" data-quality="720p">720p</a></li>
            <li><a class="dropdown-item" href="#" data-quality="480p">480p</a></li>
        `;
        
        qualityBtn.appendChild(qualityMenu);
        this.video.parentElement.querySelector('.video-controls .d-flex').appendChild(qualityBtn);
        
        // Handle quality selection
        qualityMenu.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const quality = e.target.dataset.quality;
                this.setVideoQuality(quality);
                qualityBtn.innerHTML = `<i class="fas fa-cog"></i> ${quality}`;
            });
        });
    }
    
    setVideoQuality(quality) {
        this.currentQuality = quality;
        // Implement quality switching logic here
        // This would typically involve switching to a different video source
        // based on the selected quality
    }
    
    async trackVideoView() {
        try {
            await fetch(`/videos/${this.videoId}/view`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                }
            });
        } catch (error) {
            console.error('Error tracking video view:', error);
        }
    }
    
    async likeVideo() {
        try {
            const response = await fetch(`/videos/${this.videoId}/like`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            this.updateLikeButton(data.is_liked, data.likes_count);
        } catch (error) {
            console.error('Error liking video:', error);
        }
    }
    
    async shareVideo() {
        try {
            const response = await fetch(`/videos/${this.videoId}/share`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            this.showShareOptions(data.share_url);
        } catch (error) {
            console.error('Error sharing video:', error);
        }
    }
    
    async followUser() {
        const userId = document.querySelector('.follow-button').dataset.userId;
        try {
            const response = await fetch(`/users/${userId}/follow`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            this.updateFollowButton(data.is_following);
        } catch (error) {
            console.error('Error following user:', error);
        }
    }
    
    async submitComment(e) {
        e.preventDefault();
        const form = e.target;
        const content = form.querySelector('textarea[name="content"]').value;
        
        try {
            const response = await fetch('/comments', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    video_id: this.videoId,
                    content: content,
                    parent_id: this.replyingTo
                })
            });
            
            const data = await response.json();
            this.appendComment(data.comment);
            form.reset();
            this.cancelReply();
        } catch (error) {
            console.error('Error submitting comment:', error);
        }
    }
    
    updateLikeButton(isLiked, likesCount) {
        const button = document.querySelector('.like-button');
        const icon = button.querySelector('i');
        const count = button.querySelector('span');
        
        icon.classList.toggle('text-danger', isLiked);
        count.textContent = likesCount;
    }
    
    updateFollowButton(isFollowing) {
        const button = document.querySelector('.follow-button');
        button.textContent = isFollowing ? 'Following' : 'Follow';
        button.classList.toggle('btn-primary', !isFollowing);
        button.classList.toggle('btn-secondary', isFollowing);
    }
    
    showShareOptions(shareUrl) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Share Video</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Share Link</label>
                            <div class="input-group">
                                <input type="text" class="form-control" value="${shareUrl}" readonly>
                                <button class="btn btn-outline-primary" onclick="navigator.clipboard.writeText('${shareUrl}')">
                                    Copy
                                </button>
                            </div>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary flex-grow-1" onclick="window.open('https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}')">
                                <i class="fab fa-facebook"></i> Facebook
                            </button>
                            <button class="btn btn-info flex-grow-1 text-white" onclick="window.open('https://twitter.com/intent/tweet?url=${encodeURIComponent(shareUrl)}')">
                                <i class="fab fa-twitter"></i> Twitter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
        
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }
    
    appendComment(comment) {
        const container = document.querySelector('.comments-list');
        const commentElement = this.createCommentElement(comment);
        container.insertAdjacentHTML('afterbegin', commentElement);
    }
    
    createCommentElement(comment) {
        const isReply = comment.parent_id !== null;
        return `
            <div class="comment-item mb-4 ${isReply ? 'ms-4' : ''}">
                <div class="d-flex gap-3">
                    <img src="${comment.user.profile_photo_url}" 
                         class="rounded-circle" 
                         width="40" 
                         height="40" 
                         alt="${comment.user.name}">
                    <div class="flex-grow-1">
                        <div class="d-flex align-items-center mb-1">
                            <h6 class="mb-0 me-2">${comment.user.name}</h6>
                            <small class="text-muted">${comment.created_at}</small>
                        </div>
                        <p class="mb-1">${comment.content}</p>
                        <div class="d-flex gap-3">
                            <button class="btn btn-link btn-sm p-0 text-muted" onclick="videoPlayer.likeComment(${comment.id})">
                                <i class="fas fa-heart ${comment.is_liked ? 'text-danger' : ''}"></i>
                                <span class="ms-1">${comment.likes_count}</span>
                            </button>
                            <button class="btn btn-link btn-sm p-0 text-muted" onclick="videoPlayer.replyComment(${comment.id})">
                                Reply
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    async likeComment(commentId) {
        try {
            const response = await fetch(`/comments/${commentId}/like`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            this.updateCommentLikeButton(commentId, data.is_liked, data.likes_count);
        } catch (error) {
            console.error('Error liking comment:', error);
        }
    }
    
    updateCommentLikeButton(commentId, isLiked, likesCount) {
        const button = document.querySelector(`button[onclick="videoPlayer.likeComment(${commentId})"]`);
        const icon = button.querySelector('i');
        const count = button.querySelector('span');
        
        icon.classList.toggle('text-danger', isLiked);
        count.textContent = likesCount;
    }
    
    replyComment(commentId) {
        this.replyingTo = commentId;
        const form = document.querySelector('.comment-form');
        const textarea = form.querySelector('textarea');
        textarea.focus();
        textarea.placeholder = 'Reply to comment...';
        
        // Add reply indicator
        const replyIndicator = document.createElement('div');
        replyIndicator.className = 'reply-indicator alert alert-info alert-dismissible fade show mt-2';
        replyIndicator.innerHTML = `
            Replying to a comment
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        form.insertBefore(replyIndicator, form.firstChild);
        
        // Handle cancel reply
        replyIndicator.querySelector('.btn-close').addEventListener('click', () => {
            this.cancelReply();
        });
    }
    
    cancelReply() {
        this.replyingTo = null;
        const form = document.querySelector('.comment-form');
        const textarea = form.querySelector('textarea');
        textarea.placeholder = 'Add a comment...';
        textarea.value = '';
        
        const replyIndicator = form.querySelector('.reply-indicator');
        if (replyIndicator) {
            replyIndicator.remove();
        }
    }
    
    initChapters() {
        // Add chapters button to video controls
        const chaptersBtn = document.createElement('button');
        chaptersBtn.className = 'btn btn-light btn-sm rounded-circle chapters-btn';
        chaptersBtn.innerHTML = '<i class="fas fa-list"></i>';
        chaptersBtn.setAttribute('data-bs-toggle', 'dropdown');
        
        const chaptersMenu = document.createElement('ul');
        chaptersMenu.className = 'dropdown-menu chapters-menu';
        chaptersMenu.innerHTML = `
            <li><h6 class="dropdown-header">Video Chapters</h6></li>
            <li><hr class="dropdown-divider"></li>
            <li class="chapters-list"></li>
        `;
        
        chaptersBtn.appendChild(chaptersMenu);
        this.video.parentElement.querySelector('.video-controls .d-flex').appendChild(chaptersBtn);
        
        // Load chapters
        this.loadChapters();
    }
    
    async loadChapters() {
        try {
            const response = await fetch(`/videos/${this.videoId}/chapters`);
            const data = await response.json();
            this.chapters = data.chapters;
            this.renderChapters();
        } catch (error) {
            console.error('Error loading chapters:', error);
        }
    }
    
    renderChapters() {
        const chaptersList = document.querySelector('.chapters-list');
        if (!chaptersList) return;
        
        chaptersList.innerHTML = this.chapters.map(chapter => `
            <li>
                <a class="dropdown-item" href="#" data-time="${chapter.start_time}">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>${chapter.title}</span>
                        <small class="text-muted">${this.formatTime(chapter.start_time)}</small>
                    </div>
                </a>
            </li>
        `).join('');
        
        // Add chapter click handlers
        chaptersList.querySelectorAll('.dropdown-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const time = parseFloat(e.target.dataset.time);
                this.video.currentTime = time;
                this.video.play();
            });
        });
    }
    
    initAutoplaySettings() {
        // Add autoplay settings button
        const autoplayBtn = document.createElement('button');
        autoplayBtn.className = 'btn btn-light btn-sm rounded-circle autoplay-btn';
        autoplayBtn.innerHTML = '<i class="fas fa-play-circle"></i>';
        autoplayBtn.setAttribute('data-bs-toggle', 'dropdown');
        
        const autoplayMenu = document.createElement('ul');
        autoplayMenu.className = 'dropdown-menu autoplay-menu';
        autoplayMenu.innerHTML = `
            <li><h6 class="dropdown-header">Autoplay Settings</h6></li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <div class="form-check form-switch px-3 py-2">
                    <input class="form-check-input" type="checkbox" id="enableAutoplay">
                    <label class="form-check-label" for="enableAutoplay">Enable Autoplay</label>
                </div>
            </li>
            <li>
                <div class="form-check form-switch px-3 py-2">
                    <input class="form-check-input" type="checkbox" id="nextVideo">
                    <label class="form-check-label" for="nextVideo">Play Next Video</label>
                </div>
            </li>
            <li>
                <div class="form-check form-switch px-3 py-2">
                    <input class="form-check-input" type="checkbox" id="mutedAutoplay">
                    <label class="form-check-label" for="mutedAutoplay">Muted Autoplay</label>
                </div>
            </li>
        `;
        
        autoplayBtn.appendChild(autoplayMenu);
        this.video.parentElement.querySelector('.video-controls .d-flex').appendChild(autoplayBtn);
        
        // Initialize autoplay settings
        this.initAutoplayControls();
    }
    
    initAutoplayControls() {
        const enableAutoplay = document.getElementById('enableAutoplay');
        const nextVideo = document.getElementById('nextVideo');
        const mutedAutoplay = document.getElementById('mutedAutoplay');
        
        // Load saved settings
        const savedSettings = localStorage.getItem('autoplaySettings');
        if (savedSettings) {
            this.autoplaySettings = JSON.parse(savedSettings);
            enableAutoplay.checked = this.autoplaySettings.enabled;
            nextVideo.checked = this.autoplaySettings.nextVideo;
            mutedAutoplay.checked = this.autoplaySettings.muted;
        }
        
        // Save settings on change
        enableAutoplay.addEventListener('change', () => {
            this.autoplaySettings.enabled = enableAutoplay.checked;
            this.saveAutoplaySettings();
        });
        
        nextVideo.addEventListener('change', () => {
            this.autoplaySettings.nextVideo = nextVideo.checked;
            this.saveAutoplaySettings();
        });
        
        mutedAutoplay.addEventListener('change', () => {
            this.autoplaySettings.muted = mutedAutoplay.checked;
            this.saveAutoplaySettings();
        });
    }
    
    saveAutoplaySettings() {
        localStorage.setItem('autoplaySettings', JSON.stringify(this.autoplaySettings));
    }
    
    startWatchTimeTracking() {
        // Update watch time every second
        setInterval(() => {
            if (!this.video.paused) {
                const now = Date.now();
                this.watchTime += (now - this.lastWatchTimeUpdate) / 1000;
                this.lastWatchTimeUpdate = now;
            }
        }, 1000);
        
        // Save watch time when video ends
        this.video.addEventListener('ended', () => {
            this.saveWatchTime();
        });
        
        // Save watch time when user leaves page
        window.addEventListener('beforeunload', () => {
            this.saveWatchTime();
        });
    }
    
    async saveWatchTime() {
        try {
            await fetch(`/videos/${this.videoId}/watch-time`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': this.csrfToken,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    watch_time: Math.round(this.watchTime)
                })
            });
        } catch (error) {
            console.error('Error saving watch time:', error);
        }
    }
    
    async loadVideoStatistics() {
        try {
            const response = await fetch(`/videos/${this.videoId}/statistics`);
            const data = await response.json();
            
            const watchTime = document.querySelector('.watch-time');
            const viewsCount = document.querySelector('.views-count');
            const likesCount = document.querySelector('.likes-count');
            
            if (watchTime) watchTime.textContent = this.formatTime(data.watch_time);
            if (viewsCount) viewsCount.textContent = data.views_count;
            if (likesCount) likesCount.textContent = data.likes_count;
        } catch (error) {
            console.error('Error loading video statistics:', error);
        }
    }
}

// Initialize
const videoPlayer = new VideoPlayer(); 