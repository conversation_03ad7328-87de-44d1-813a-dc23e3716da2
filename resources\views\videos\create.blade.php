@extends('layouts.app')

@section('content')
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="upload-container">
                <h4 class="mb-4">Create New Video</h4>
                <form action="{{ route('videos.store') }}" method="POST" enctype="multipart/form-data" id="videoForm">
                    @csrf
                    
                    <!-- Video Upload -->
                    <div class="mb-4">
                        <label class="form-label">Upload Video</label>
                        <div class="upload-area" id="uploadArea">
                            <input type="file" 
                                   name="video" 
                                   id="videoInput" 
                                   class="form-control" 
                                   accept="video/*" 
                                   required>
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <h5 class="mb-3">Drag and drop your video here</h5>
                                <p class="text-muted mb-3">or</p>
                                <button type="button" class="btn btn-primary px-4" onclick="document.getElementById('videoInput').click()">
                                    Choose File
                                </button>
                                <p class="text-muted mt-3 mb-0">
                                    <small>Maximum file size: 100MB</small><br>
                                    <small>Supported formats: MP4, MOV, OGG, AVI, WMV, FLV, MKV, WebM, 3GP, MPG, MPEG, SWF, VOB, TS, M4V</small>
                                </p>
                            </div>
                            <div id="previewArea" class="d-none">
                                <video id="videoPreview" class="w-100 rounded" controls></video>
                                <div class="text-center mt-3">
                                    <button type="button" class="btn btn-outline-danger" onclick="removeVideo()">
                                        <i class="fas fa-trash-alt me-2"></i>Remove Video
                                    </button>
                                </div>
                            </div>
                        </div>
                        @error('video')
                        <div class="text-danger mt-2">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Video Details -->
                    <div class="row">
                        <div class="col-md-8">
                            <!-- Title -->
                            <div class="mb-4">
                                <label class="form-label">Title</label>
                                <input type="text" 
                                       name="title" 
                                       class="form-control @error('title') is-invalid @enderror" 
                                       value="{{ old('title') }}" 
                                       placeholder="Enter a descriptive title"
                                       required>
                                @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <!-- Description -->
                            <div class="mb-4">
                                <label class="form-label">Description</label>
                                <textarea name="description" 
                                          class="form-control @error('description') is-invalid @enderror" 
                                          rows="4" 
                                          placeholder="Tell viewers about your video">{{ old('description') }}</textarea>
                                @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <!-- Privacy Settings -->
                            <div class="mb-4">
                                <label class="form-label d-block">Privacy</label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" 
                                           class="btn-check" 
                                           name="is_public" 
                                           id="public" 
                                           value="1" 
                                           {{ old('is_public', true) ? 'checked' : '' }}>
                                    <label class="btn btn-outline-primary" for="public">
                                        <i class="fas fa-globe me-2"></i>Public
                                    </label>
                                    
                                    <input type="radio" 
                                           class="btn-check" 
                                           name="is_public" 
                                           id="private" 
                                           value="0" 
                                           {{ old('is_public') === false ? 'checked' : '' }}>
                                    <label class="btn btn-outline-primary" for="private">
                                        <i class="fas fa-lock me-2"></i>Private
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Submit Button -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <a href="{{ route('videos.index') }}" class="btn btn-link text-muted">
                            <i class="fas fa-arrow-left me-2"></i>Back to Videos
                        </a>
                        <button type="submit" class="btn btn-primary px-4">
                            <i class="fas fa-cloud-upload-alt me-2"></i>Upload Video
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function removeVideo() {
    document.getElementById('videoInput').value = '';
    document.getElementById('previewArea').classList.add('d-none');
    document.getElementById('uploadArea').querySelector('.upload-content').classList.remove('d-none');
}

document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const videoInput = document.getElementById('videoInput');
    const previewArea = document.getElementById('previewArea');
    const videoPreview = document.getElementById('videoPreview');
    const uploadContent = uploadArea.querySelector('.upload-content');

    // Drag and drop functionality
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    function highlight(e) {
        uploadArea.classList.add('border-primary');
    }

    function unhighlight(e) {
        uploadArea.classList.remove('border-primary');
    }

    uploadArea.addEventListener('drop', handleDrop, false);

    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles(files);
    }

    videoInput.addEventListener('change', function() {
        handleFiles(this.files);
    });

    function handleFiles(files) {
        if (files.length > 0) {
            const file = files[0];
            if (file.type.startsWith('video/')) {
                uploadContent.classList.add('d-none');
                previewArea.classList.remove('d-none');
                videoPreview.src = URL.createObjectURL(file);
            }
        }
    }
});
</script>
@endpush

@vite(['resources/css/video.css'])
@endsection
