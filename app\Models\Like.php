<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Like extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'video_id'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function video()
    {
        return $this->belongsTo(Video::class);
    }

    public static function toggle($userId, $videoId)
    {
        $like = static::where('user_id', $userId)
            ->where('video_id', $videoId)
            ->first();

        if ($like) {
            $like->delete();
            return false; // Unliked
        }

        static::create([
            'user_id' => $userId,
            'video_id' => $videoId
        ]);

        return true; // Liked
    }
}
