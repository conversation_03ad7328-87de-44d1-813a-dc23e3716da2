@extends('layouts.app')

@section('content')
<div class="video-feed-container">
    <div class="container-fluid">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-9">
                <div class="video-feed">
                    @forelse($videos as $video)
                    <div class="video-card slide-up">
                        <a href="{{ route('videos.show', $video) }}" class="text-decoration-none">
                            <div class="video-wrapper">
                                <video 
                                    src="{{ asset('storage/' . $video->video_path) }}"
                                    loop
                                    muted
                                    preload="metadata"
                                    playsinline
                                    data-video-id="{{ $video->id }}"
                                ></video>
                                
                                <!-- Video Overlay -->
                                <div class="video-overlay">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <div class="d-flex align-items-center">
                                            <img src="{{ $video->user->profile_photo_url }}" 
                                                 class="creator-avatar" 
                                                 alt="{{ $video->user->name }}">
                                            <div class="ms-2">
                                                <h6 class="mb-0 text-white">{{ $video->user->name }}</h6>
                                                <small class="text-white-50">{{ $video->created_at->diffForHumans() }}</small>
                                            </div>
                                        </div>
                                        <div class="video-duration px-2 py-1 rounded bg-black bg-opacity-50">
                                            {{ gmdate('i:s', $video->duration) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                        
                        <!-- Video Info -->
                        <div class="video-info">
                            <a href="{{ route('videos.show', $video) }}" class="text-decoration-none">
                                <h5 class="video-title text-body dark:text-white">{{ $video->title }}</h5>
                            </a>
                            
                            <div class="video-stats">
                                <div class="video-stat">
                                    <i class="fas fa-eye"></i>
                                    <span>{{ number_format($video->views_count) }}</span>
                                </div>
                                <div class="video-stat">
                                    <i class="fas fa-heart"></i>
                                    <span>{{ number_format($video->likes_count) }}</span>
                                </div>
                                <div class="video-stat">
                                    <i class="fas fa-comment"></i>
                                    <span>{{ number_format($video->comments_count) }}</span>
                                </div>
                            </div>
                            
                            <div class="video-controls">
                                <button class="video-control-btn {{ $video->is_liked ? 'liked' : '' }}" 
                                        data-video-id="{{ $video->id }}"
                                        title="Like">
                                    <i class="fas fa-heart"></i>
                                </button>
                                <button class="video-control-btn" 
                                        data-video-id="{{ $video->id }}"
                                        title="Share">
                                    <i class="fas fa-share"></i>
                                </button>
                                <button class="video-control-btn" 
                                        data-video-id="{{ $video->id }}"
                                        title="Comment">
                                    <i class="fas fa-comment"></i>
                                </button>
                            </div>
                            
                            @if($video->tags)
                            <div class="tags-container">
                                @foreach($video->tags as $tag)
                                <span class="tag">{{ $tag }}</span>
                                @endforeach
                            </div>
                            @endif
                        </div>
                    </div>
                    @empty
                    <div class="col-12 text-center py-5">
                        <div class="empty-state">
                            <i class="fas fa-video mb-4 text-muted" style="font-size: 3rem;"></i>
                            <h3>No Videos Yet</h3>
                            <p class="text-muted">Be the first to upload a video!</p>
                            <a href="{{ route('videos.create') }}" class="btn btn-primary">
                                <i class="fas fa-cloud-upload-alt me-2"></i>Upload Video
                            </a>
                        </div>
                    </div>
                    @endforelse
                </div>
                
                <!-- Pagination -->
                @if($videos->hasPages())
                <div class="d-flex justify-content-center mt-4">
                    {{ $videos->links() }}
                </div>
                @endif
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-3">
                <!-- Trending Videos -->
                <div class="sidebar-container mb-4">
                    <div class="sidebar-header">
                        <h5 class="sidebar-title">
                            <i class="fas fa-fire-alt me-2 text-danger"></i>Trending
                        </h5>
                    </div>
                    <div class="sidebar-content">
                        @foreach($trendingVideos as $trending)
                        <a href="{{ route('videos.show', $trending) }}" class="text-decoration-none">
                            <div class="d-flex gap-3 mb-3">
                                <div class="video-thumbnail" style="width: 120px;">
                                    <img src="{{ Storage::url($trending->thumbnail_path) }}" 
                                         alt="Thumbnail">
                                    <span class="video-duration">{{ gmdate('i:s', $trending->duration) }}</span>
                                </div>
                                <div>
                                    <h6 class="mb-1 text-body dark:text-white">{{ Str::limit($trending->title, 50) }}</h6>
                                    <div class="video-stats">
                                        <div class="video-stat">
                                            <i class="fas fa-eye"></i>
                                            <span>{{ number_format($trending->views_count) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                        @endforeach
                    </div>
                </div>
                
                <!-- Suggested Creators -->
                <div class="sidebar-container">
                    <div class="sidebar-header">
                        <h5 class="sidebar-title">
                            <i class="fas fa-users me-2 text-primary"></i>Suggested Creators
                        </h5>
                    </div>
                    <div class="sidebar-content">
                        @foreach($suggestedCreators as $creator)
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <div class="d-flex align-items-center">
                                <img src="{{ $creator->profile_photo_url }}" 
                                     class="creator-avatar me-3"
                                     alt="{{ $creator->name }}">
                                <div>
                                    <h6 class="mb-0">{{ $creator->name }}</h6>
                                    <small class="text-muted">
                                        <i class="fas fa-users me-1"></i>
                                        {{ number_format($creator->followers_count) }} followers
                                    </small>
                                </div>
                            </div>
                            <button class="btn btn-primary btn-sm rounded-pill follow-button" 
                                    data-user-id="{{ $creator->id }}">
                                {{ $creator->is_followed ? 'Following' : 'Follow' }}
                            </button>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-play videos when they come into view
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            const video = entry.target;
            if (entry.isIntersecting) {
                video.play().catch(() => {
                    // Autoplay was prevented
                    console.log('Autoplay prevented');
                });
            } else {
                video.pause();
            }
        });
    }, { threshold: 0.5 });

    document.querySelectorAll('.video-wrapper video').forEach(video => {
        observer.observe(video);
        
        // Add hover play/pause
        const wrapper = video.closest('.video-wrapper');
        wrapper.addEventListener('mouseenter', () => {
            video.play().catch(() => {});
        });
        wrapper.addEventListener('mouseleave', () => {
            video.pause();
        });
    });

    // Like button functionality
    document.querySelectorAll('.video-control-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            const videoId = btn.dataset.videoId;
            if (btn.classList.contains('liked')) {
                // Unlike
                btn.classList.remove('liked');
            } else {
                // Like
                btn.classList.add('liked');
            }
        });
    });
});
</script>
@endpush

@vite(['resources/css/video.css'])
@endsection
