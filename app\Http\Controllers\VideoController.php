<?php

namespace App\Http\Controllers;

use App\Models\Video;
use App\Models\Like;
use App\Models\Share;
use App\Jobs\ProcessVideo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use ProtoneMedia\LaravelFFMpeg\Support\FFmpeg;
use App\Models\User;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class VideoController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth')->except(['index', 'show']);
    }

    public function index()
    {
        $videos = Video::with(['user', 'likes', 'comments', 'shares'])
            ->where('status', 'published')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        $trendingVideos = Video::with(['user'])
            ->where('status', 'published')
            ->where('created_at', '>=', now()->subDays(7))
            ->orderBy('views', 'desc')
            ->select('id', 'title', 'thumbnail_path', 'views', 'created_at')
            ->limit(5)
            ->get();

        $suggestedCreators = User::withCount('followers')
            ->orderBy('followers_count', 'desc')
            ->limit(5)
            ->get();

        return view('videos.index', compact('videos', 'trendingVideos', 'suggestedCreators'));
    }

    public function create()
    {
        return view('videos.create');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'video' => 'required|file|mimes:mp4,mov,ogg,qt,avi,wmv,flv,mkv,webm,3gp,mpg,mpeg,swf,vob,ts,m4v|max:102400', // 100MB max
            'is_public' => 'boolean'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            // Store video file in admin/videos folder
            $videoFile = $request->file('video');
            $fileName = date('YmdHis') . '_' . uniqid() . '.' . $videoFile->getClientOriginalExtension();
            
            // Ensure the directory exists
            $uploadPath = public_path('admin/videos');
            if (!file_exists($uploadPath)) {
                mkdir($uploadPath, 0777, true);
            }
            
            // Move the file and get the path
            $videoFile->move($uploadPath, $fileName);
            $videoPath = 'admin/videos/' . $fileName;

            // Get file size and other metadata
            $fileSize = $videoFile->getSize();
            $mimeType = $videoFile->getMimeType();
            $originalName = $videoFile->getClientOriginalName();

            // Create video record in database
            $videoModel = Video::create([
                'user_id' => auth()->id(),
                'title' => $request->title,
                'description' => $request->description,
                'video_path' => $videoPath,
                'duration' => 0, // Will be updated after processing
                'status' => 'processing',
                'is_public' => $request->is_public ?? true,
                'share_token' => Str::random(32),
                'views' => 0,
                'likes' => 0,
                'shares' => 0,
                'earnings' => 0,
                'watch_time' => 0,
                'metadata' => [
                    'original_name' => $originalName,
                    'mime_type' => $mimeType,
                    'size' => $fileSize,
                    'extension' => $videoFile->getClientOriginalExtension(),
                    'uploaded_at' => now()->toIso8601String()
                ]
            ]);

            // Log successful upload
            Log::info('Video uploaded successfully', [
                'video_id' => $videoModel->id,
                'file_name' => $fileName,
                'file_size' => $fileSize,
                'user_id' => auth()->id()
            ]);

            // Process video in background
            ProcessVideo::dispatch($videoModel);

            return redirect()->route('videos.show', $videoModel)
                ->with('success', 'Video uploaded successfully and is being processed.');

        } catch (\Exception $e) {
            Log::error('Video upload failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to upload video. Please try again.')
                ->withInput();
        }
    }

    public function show(Video $video)
    {
        if (!$video->is_public && $video->user_id !== auth()->id()) {
            abort(403);
        }

        $video->load(['user', 'comments.user', 'likes']);
        $video->increment('views');

        return view('videos.show', compact('video'));
    }

    public function edit(Video $video)
    {
        $this->authorize('update', $video);
        return view('videos.edit', compact('video'));
    }

    public function update(Request $request, Video $video)
    {
        $this->authorize('update', $video);

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_public' => 'boolean'
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $video->update($request->only(['title', 'description', 'is_public']));

        return redirect()->route('videos.show', $video)
            ->with('success', 'Video updated successfully.');
    }

    public function destroy(Video $video)
    {
        $this->authorize('delete', $video);

        // Delete video file from storage
        Storage::disk('s3')->delete($video->video_path);
        if ($video->thumbnail_path) {
            Storage::disk('s3')->delete($video->thumbnail_path);
        }

        $video->delete();

        return redirect()->route('videos.index')
            ->with('success', 'Video deleted successfully.');
    }

    public function like(Video $video)
    {
        $liked = Like::toggle(auth()->id(), $video->id);
        
        if ($liked) {
            $video->increment('likes');
        } else {
            $video->decrement('likes');
        }

        return response()->json([
            'likes_count' => $video->likes,
            'is_liked' => $liked
        ]);
    }

    public function share(Request $request, Video $video)
    {
        $validator = Validator::make($request->all(), [
            'platform' => 'required|string|in:facebook,twitter,whatsapp'
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $share = Share::createShare(
            auth()->id(),
            $video->id,
            $request->platform
        );

        $video->increment('shares');

        return response()->json([
            'shares_count' => $video->shares,
            'share_url' => $share->share_url
        ]);
    }
}
