<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Video extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'video_path',
        'thumbnail_path',
        'duration',
        'status',
        'metadata',
        'views',
        'likes',
        'shares',
        'earnings',
        'is_public',
        'share_token',
        'watch_time',
        'chapters'
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_public' => 'boolean',
        'earnings' => 'decimal:2',
        'chapters' => 'array',
        'watch_time' => 'integer'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    public function likes()
    {
        return $this->hasMany(Like::class);
    }

    public function shares()
    {
        return $this->hasMany(Share::class);
    }

    public function getThumbnailUrlAttribute()
    {
        return $this->thumbnail_path ? asset('storage/' . $this->thumbnail_path) : null;
    }

    public function getVideoUrlAttribute()
    {
        return $this->video_path ? asset('storage/' . $this->video_path) : null;
    }

    public function getShareUrlAttribute()
    {
        return route('videos.show', ['video' => $this, 'token' => $this->share_token]);
    }

    public function getEmbedCodeAttribute()
    {
        return '<iframe src="' . route('videos.embed', $this) . '" frameborder="0" allowfullscreen></iframe>';
    }

    public function incrementViews()
    {
        $this->increment('views');
    }

    public function updateWatchTime($seconds)
    {
        $this->increment('watch_time', $seconds);
    }

    public function addChapter($title, $startTime, $endTime = null)
    {
        $chapters = $this->chapters ?? [];
        $chapters[] = [
            'id' => uniqid(),
            'title' => $title,
            'start_time' => $startTime,
            'end_time' => $endTime
        ];
        $this->update(['chapters' => $chapters]);
    }

    public function updateChapter($chapterId, $title, $startTime, $endTime = null)
    {
        $chapters = $this->chapters ?? [];
        $chapters = array_map(function($chapter) use ($chapterId, $title, $startTime, $endTime) {
            if ($chapter['id'] === $chapterId) {
                return [
                    'id' => $chapterId,
                    'title' => $title,
                    'start_time' => $startTime,
                    'end_time' => $endTime
                ];
            }
            return $chapter;
        }, $chapters);
        $this->update(['chapters' => $chapters]);
    }

    public function deleteChapter($chapterId)
    {
        $chapters = $this->chapters ?? [];
        $chapters = array_filter($chapters, function($chapter) use ($chapterId) {
            return $chapter['id'] !== $chapterId;
        });
        $this->update(['chapters' => array_values($chapters)]);
    }
}
