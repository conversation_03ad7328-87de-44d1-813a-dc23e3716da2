<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('videos', function (Blueprint $table) {
            $table->string('share_token')->nullable()->unique()->after('is_public');
            $table->integer('watch_time')->default(0)->after('views');
            $table->json('chapters')->nullable()->after('watch_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('videos', function (Blueprint $table) {
            $table->dropColumn(['share_token', 'watch_time', 'chapters']);
        });
    }
};
